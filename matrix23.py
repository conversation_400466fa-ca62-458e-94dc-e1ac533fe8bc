from app import app
import MetaTrader5 as mt5
import numpy as np
from numba import njit
import pandas as pd
from datetime import datetime, timedelta
import dash
from dash import html, Input, Output, State
import atexit
import plotly.graph_objects as go
import pytz
import scipy.optimize as sco
import json
import time
from joblib import Parallel, delayed
import os
import psutil
from itertools import combinations
import logging # Import logging module

from ratio_calcs import portfolio_variance
from func_mt5 import calculate_returns, fetch_data, convert_log_to_arithmetic_returns
from func_rest import cached_process_combo, get_optimal_cores, timing_decorator
from func_gfx import create_combined_mpt_string
from func_portfolio import generate_portfolio_suggestions, find_recommended_portfolios, combine_portfolios
from weekend_utils import should_freeze_updates, should_use_friday_data, cache_friday_data, get_cached_friday_data, log_weekend_status
#from market_phase import generate_market_phase_figures # <-- Import the new function

import layout
import mpt_tracker
import port_table
import mpt_allocation
import mpt_recommendation
#import matrix_phases

app.layout = layout.layout # Assign layout after all imports

# --- Basic Logging Configuration ---
# Configure logging to output INFO level messages to the console
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__) # Optional: Get a logger for this specific module if needed

# Process chunks with controlled parallelism and CPU throttling
def get_adaptive_n_jobs():
    """Dynamically determine optimal number of cores based on current load"""
    current_cpu = psutil.cpu_percent(interval=0.1)
    #max_jobs = multiprocessing.cpu_count()

    # Return specific numbers of cores based on CPU load
    if current_cpu > 80:
        return 12  # Use exactly 4 cores when CPU is very high
    elif current_cpu > 70:
        return 18  # Use exactly 6 cores when CPU is moderately high
    elif current_cpu > 60:
        return 21  # Use exactly 6 cores when CPU is moderately high
    else:
        return 24  # Use exactly 8 cores when CPU is normal
        
# --- Global Joblib Parallel Instance ---
# Create a single Parallel instance to be reused by callbacks
# Configure it once here
parallel_executor = Parallel(
    n_jobs=get_adaptive_n_jobs(), # We'll set n_jobs dynamically later if needed, or keep fixed
    #n_jobs=8, # Start with a fixed number, can be adjusted
    verbose=10,
    prefer="processes",
    backend="loky",
    batch_size="auto",
    max_nbytes='10M', # Limit memory per job
    # timeout=30 # Consider adding timeout if jobs might hang
)
# Removed the explicit atexit handler for parallel_executor cleanup.
# Relying on loky's default cleanup mechanisms.


##################################################
#---MAIN (CACHE - MEMORY - CPU - TIMING)---#
# Set process priority to below normal to allow other system processes to run
try:
    process = psutil.Process(os.getpid())
    if os.name == 'nt':  # Windows
        process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
    else:  # Unix-based
        process.nice(10)  # Higher nice value = lower priority
except Exception as e:
    print(f"Error getting psutil process: {e}")
    pass

@app.callback(
    [Output('portfolio-table', 'children'),
     Output('efficient-frontier', 'figure'),
     Output('portfolio-returns', 'figure'),
     Output('optimization-running-store', 'data'),
     Output('combined-mpt-weights', 'value'),
     Output('cvar-frontier', 'figure'),
     Output('recommended-portfolios-store', 'data'),
     Output('recommended-cvar-portfolios-store', 'data'),
     Output('clicked-portfolio-store', 'data')],  # Added Output for the new store
    [Input('interval-optim', 'n_intervals'),
     Input('efficient-frontier', 'clickData'),
     Input('cvar-frontier', 'clickData'),
     Input('frontier-option', 'value'),
     Input('annotation-total-weight', 'value')],
    [State('optimization-running-store', 'data'),
     State('clicked-portfolio-store', 'data')]  # Added State for the new store
)
@timing_decorator
def update_portfolio_optimization(n_intervals, ef_clickData, cvar_clickData, frontier_option, annotation_total_weight, optimization_running, stored_click_data):
    # Log weekend status for debugging
    log_weekend_status()

    # Get the trigger ID to understand what triggered this callback
    trigger_id = None
    if dash.callback_context.triggered:
        trigger_id = dash.callback_context.triggered[0]['prop_id'].split('.')[0]

    # Check if we should use Friday data during weekend
    cache_key = f"portfolio_optimization_{frontier_option}_{annotation_total_weight}"

    if should_use_friday_data():
        # During weekends, only run optimization if triggered by click, not by timer
        if trigger_id == 'interval-optim' and not stored_click_data:
            print("Weekend mode: Preventing timer-triggered optimization, no stored click data")
            raise dash.exceptions.PreventUpdate

        # Try to get cached Friday data
        cached_data = get_cached_friday_data(cache_key)
        if cached_data is not None:
            print("Weekend mode: Using cached Friday portfolio optimization data")
            return cached_data
        else:
            print("Weekend mode: No cached data available, using Friday data with current parameters")

    # If optimization is already running, prevent update
    if optimization_running:
        raise dash.exceptions.PreventUpdate

    # Determine what triggered the callback
    ctx = dash.callback_context
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else 'interval-optim' # Default to timer if no trigger

    # If timer triggered AND optimization is running, skip this update
    if trigger_id == 'interval-optim' and optimization_running:
        # Return the previous values unchanged + True to indicate still running
        raise dash.exceptions.PreventUpdate

    # --- Configuration ---
    #checks_per_pair = 50  # Minimum occurrences for a pair in size 3, 4, 5 combos
    
    combined_mpt_string = ""
    suggested_portfolios = []
    clicked_portfolio_data_to_store = None # Initialize store output
    # Initialize empty values to handle early returns
    empty_fig = go.Figure()
    empty_fig.update_layout(template="plotly_dark") # Ensure empty figs use dark theme
    
    # Determine clickData based on trigger or stored data
    clickData = None
    if trigger_id == 'efficient-frontier' and ef_clickData and 'points' in ef_clickData and ef_clickData['points']:
        clickData = ef_clickData
        # Store the customdata from the click
        try:
            clicked_portfolio_data_to_store = json.loads(ef_clickData['points'][0]['customdata'])
            # Add flag indicating which frontier was clicked
            clicked_portfolio_data_to_store['frontier_type'] = 'efficient-frontier'
        except Exception as e:
            print(f"Error storing EF click data: {e}")
            clicked_portfolio_data_to_store = None
    elif trigger_id == 'cvar-frontier' and cvar_clickData and 'points' in cvar_clickData and cvar_clickData['points']:
        clickData = cvar_clickData
        # Store the customdata from the click
        try:
            clicked_portfolio_data_to_store = json.loads(cvar_clickData['points'][0]['customdata'])
             # Add flag indicating which frontier was clicked
            clicked_portfolio_data_to_store['frontier_type'] = 'cvar-frontier'
        except Exception as e:
            print(f"Error storing CVaR click data: {e}")
            clicked_portfolio_data_to_store = None
    elif trigger_id == 'interval-optim' and stored_click_data:
        # If timer triggered, use the stored click data
        print("Timer triggered, using stored click data.")
        clickData = {'points': [{'customdata': json.dumps(stored_click_data)}]} # Reconstruct clickData format
        # Keep the stored data as is for the next interval
        clicked_portfolio_data_to_store = stored_click_data
    else:
        # No valid click or stored data, clear the store
        clicked_portfolio_data_to_store = None
    
    # Set optimization as running
    optimization_running = True
    
    try:
        current_usage = psutil.cpu_percent(interval=0.5)
        if current_usage > 70:
            time.sleep(1.5)  # Add delay if CPU is already high

        # Define the 28 symbols.
        symbols = [
            "EURUSD", "EURGBP", "EURAUD", "EURNZD", "EURCHF", "EURCAD",
            "GBPUSD", "GBPAUD", "GBPNZD", "GBPCHF", "GBPCAD",
            "AUDUSD", "AUDNZD", "AUDCHF", "AUDCAD",
            "NZDUSD", "NZDCHF", "NZDCAD",
            "USDCHF", "USDCAD",
            "CADCHF",
            "EURJPY", "GBPJPY", "AUDJPY", "NZDJPY", "CADJPY", "CHFJPY", "USDJPY"
            #"XAUUSD"
        ]

        def calculate_business_hours(required_hours):
            """Calculate how many calendar hours to fetch to get the required business hours"""
            now = datetime.now(pytz.timezone('Europe/Bucharest'))
            current_day = now.weekday()  # 0 = Monday, 6 = Sunday
            
            hours_needed = required_hours + 3
            total_hours = 0
            current_hour = now.hour
            
            # Start from the current time and work backward
            day_pointer = current_day
            hour_pointer = current_hour
            
            while hours_needed > 0:
                # If we're on a weekday (0-4)
                if day_pointer < 5:
                    # Calculate hours available in this day
                    hours_available = hour_pointer if day_pointer == current_day else 24
                    hours_to_take = min(hours_needed, hours_available)
                    
                    hours_needed -= hours_to_take
                    total_hours += hours_to_take
                else:
                    # Weekend day - add to total hours without reducing hours_needed
                    total_hours += 24
                
                # Move to previous day
                hour_pointer = 24  # Reset for all days except the first
                day_pointer = (day_pointer - 1) % 7
            
            return total_hours

        # Choose market data based on the frontier-option radio button.
        # MODIFIED: We'll now fetch 3x the data needed for each option
        if frontier_option == 'today':
            # For today, get only business hours from current day
            now = datetime.now(pytz.timezone('Europe/Bucharest'))
            midnight = now.replace(hour=0, minute=0, second=0, microsecond=0)
            hours_current = (now - midnight).total_seconds() / 3600

            # If it's a weekend, use full Friday data (00:00 to 23:59) instead of rolling window
            if now.weekday() >= 5:  # weekend check
                from weekend_utils import get_last_friday_end
                friday_end = get_last_friday_end()
                # Use full Friday data from 00:00 to 23:59
                friday_start = friday_end.replace(hour=0, minute=0, second=0, microsecond=0)
                print(f"Weekend mode: Using Friday data from {friday_start} to {friday_end}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=friday_start, end_time=friday_end)
                business_hours_window = 24  # Use full 24 hours for display
            else:
                hours_to_fetch = hours_current * 5  # 3x current day's hours
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)
                business_hours_window = hours_current  # Keep original window for display
            
        elif frontier_option == '240':
            # Check if it's weekend and use data ending at Friday
            if now.weekday() >= 5:  # weekend check
                from weekend_utils import get_last_friday_end
                friday_end = get_last_friday_end()
                # Calculate start time for 240 hours ending at Friday
                friday_start = friday_end - timedelta(hours=240)
                print(f"Weekend mode: Fetching 240 hours of data ending at Friday: {friday_start} to {friday_end}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=friday_start, end_time=friday_end)
            else:
                # Get 3x the 240 business hours
                hours_to_fetch = calculate_business_hours(240 * 5)
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)
            business_hours_window = 240  # Keep original window for display

        elif frontier_option == '120':
            # Check if it's weekend and use data ending at Friday
            if now.weekday() >= 5:  # weekend check
                from weekend_utils import get_last_friday_end
                friday_end = get_last_friday_end()
                # Calculate start time for 120 hours ending at Friday
                friday_start = friday_end - timedelta(hours=120)
                print(f"Weekend mode: Fetching 120 hours of data ending at Friday: {friday_start} to {friday_end}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=friday_start, end_time=friday_end)
            else:
                # Get 3x the 120 business hours
                hours_to_fetch = calculate_business_hours(120 * 5)
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)
            business_hours_window = 120  # Keep original window for display

        elif frontier_option == '72':
            # Check if it's weekend and use data ending at Friday
            if now.weekday() >= 5:  # weekend check
                from weekend_utils import get_last_friday_end
                friday_end = get_last_friday_end()
                # Calculate start time for 72 hours ending at Friday
                friday_start = friday_end - timedelta(hours=72)
                print(f"Weekend mode: Fetching 72 hours of data ending at Friday: {friday_start} to {friday_end}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=friday_start, end_time=friday_end)
            else:
                # Get 3x the 72 business hours
                hours_to_fetch = calculate_business_hours(72 * 5)
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)
            business_hours_window = 72  # Keep original window for display

        else:
            # For 24-hour option
            # Check if it's weekend and use data ending at Friday
            if now.weekday() >= 5:  # weekend check
                from weekend_utils import get_last_friday_end
                friday_end = get_last_friday_end()
                # Calculate start time for 24 hours ending at Friday
                friday_start = friday_end - timedelta(hours=24)
                print(f"Weekend mode: Fetching 24 hours of data ending at Friday: {friday_start} to {friday_end}")
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                                       start_time=friday_start, end_time=friday_end)
            else:
                # Get 3x the 24 business hours
                hours_to_fetch = calculate_business_hours(24 * 5)
                market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0, hours=hours_to_fetch)
            business_hours_window = 24  # Keep original window for display

        # Calculate returns for all cases - this is the full dataset for statistics
        full_returns_df = calculate_returns(market_data)
        
        # Make a copy for display purposes
        returns_df = full_returns_df.copy()

        # Filter the display data based on business hours window
        if not returns_df.empty:
            # Filter out non-business days (weekends) from both datasets
            business_days_mask = returns_df.index.dayofweek < 5
            returns_df = returns_df[business_days_mask]
            full_returns_df = full_returns_df[business_days_mask]
            
            # Special case for 24-hour option and "today" option during weekends
            if frontier_option == '24' or (frontier_option == 'today' and now.weekday() >= 5):
                # Count how many business hours we have, working backward from the most recent data
                if len(returns_df) > 0:
                    # Get the most recent timestamp
                    #latest_timestamp = returns_df.index[-1]
                    business_rows_needed = 24 * 4  # 4 rows per hour for M15

                    # Take exactly the number of rows that correspond to 24 business hours for display
                    if len(returns_df) > business_rows_needed:
                        returns_df = returns_df.tail(business_rows_needed)
            else:
                # For other options, calculate how many rows we need for the display window
                rows_per_hour = 4  # For M15 timeframe
                business_rows_needed = int(business_hours_window * rows_per_hour)
                
                # Filter the display dataset to the requested window size
                if len(returns_df) > business_rows_needed:
                    returns_df = returns_df.tail(business_rows_needed)

        # Continue with the empty check
        if full_returns_df.empty:
            # Return empty figures for MPT/CVaR plots
            return ("No Data", empty_fig, empty_fig, False, "", empty_fig, None, None, None) # MPT/CVaR outputs (9 total)

        # Prepare lists to hold candidate portfolios.
        all_portfolios_minvar = []
        all_portfolios_maxsharpe = []
        all_portfolios_maxsortino = []
        all_portfolios_maxomega = []
        all_portfolios_maxcalmar = []
        all_portfolios_maxmodsharpe = []
        all_candidates_composite = []  # For high sharpe & low minvar portfolios.
        ss_candidates_composite = []  # For high sharpe & low minvar portfolios.

        # Counters and limits.
        #counter = 0
        max_combos = 100000  # increased iterations
        #checks_per_pair = 50 #how many pairs for limit / for triples quadraples, etc
        matches = { 3, 4, 5 } #how many pairs for limit / for triples quadraples, etc
        # These dictionaries record per-symbol occurrences in each table.
        #occurrence_minvar = {}
        #occurrence_maxsharpe = {}
        #occurrence_maxsortino = {}
        #occurrence_maxomega = {}
        #occurrence_maxcalmar = {}
        #occurrence_maxmodsharpe = {}
        #occurrence_bestcomp = {}
        #occurrence_bestmaxss = {}

        # Loop over combination sizes 3 to 5.
        start_time = datetime.now()
        
        print(f"Starting portfolio optimization with {len(symbols)} symbols...")
        # Add this near your other parameters
        correlation_max_threshold = 0.5
        correlation_min_threshold = -0.5

        # After defining other parameters like correlation thresholds
        print(f"Starting portfolio optimization with {len(symbols)} symbols...")
        print(f"Precomputing correlation matrix for {len(symbols)} symbols...")
        full_corr_matrix = full_returns_df[symbols].corr()

        # --- Market Phase Analysis is now handled by a separate callback ---

        # Define separate functions for each combination size
        @njit
        def check_valid_combinations_of_size_3(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 3"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        # Check all pairwise correlations
                        corr12 = all_correlations[i, j]
                        corr13 = all_correlations[i, k]
                        corr23 = all_correlations[j, k]
                        
                        if (min_corr <= corr12 <= max_corr and 
                            min_corr <= corr13 <= max_corr and 
                            min_corr <= corr23 <= max_corr):
                            valid_indices.append((i, j, k))
            
            return valid_indices

        @njit
        def check_valid_combinations_of_size_4(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 4"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        for l in range(k+1, n_symbols):
                            # Check all pairwise correlations (6 pairs)
                            corr12 = all_correlations[i, j]
                            corr13 = all_correlations[i, k]
                            corr14 = all_correlations[i, l]
                            corr23 = all_correlations[j, k]
                            corr24 = all_correlations[j, l]
                            corr34 = all_correlations[k, l]
                            
                            if (min_corr <= corr12 <= max_corr and 
                                min_corr <= corr13 <= max_corr and
                                min_corr <= corr14 <= max_corr and
                                min_corr <= corr23 <= max_corr and
                                min_corr <= corr24 <= max_corr and
                                min_corr <= corr34 <= max_corr):
                                valid_indices.append((i, j, k, l))
            
            return valid_indices

        @njit
        def check_valid_combinations_of_size_5(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 5"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        for l in range(k+1, n_symbols):
                            for m in range(l+1, n_symbols):
                                # Check all 10 pairwise correlations
                                corr12 = all_correlations[i, j]
                                corr13 = all_correlations[i, k]
                                corr14 = all_correlations[i, l]
                                corr15 = all_correlations[i, m]
                                corr23 = all_correlations[j, k]
                                corr24 = all_correlations[j, l]
                                corr25 = all_correlations[j, m]
                                corr34 = all_correlations[k, l]
                                corr35 = all_correlations[k, m]
                                corr45 = all_correlations[l, m]
                                
                                if (min_corr <= corr12 <= max_corr and 
                                    min_corr <= corr13 <= max_corr and
                                    min_corr <= corr14 <= max_corr and
                                    min_corr <= corr15 <= max_corr and
                                    min_corr <= corr23 <= max_corr and
                                    min_corr <= corr24 <= max_corr and
                                    min_corr <= corr25 <= max_corr and
                                    min_corr <= corr34 <= max_corr and
                                    min_corr <= corr35 <= max_corr and
                                    min_corr <= corr45 <= max_corr):
                                    valid_indices.append((i, j, k, l, m))
            
            return valid_indices

        @njit
        def check_valid_combinations_of_size_6(all_correlations, max_corr, min_corr):
            """Check valid combinations of size 6"""
            valid_indices = []
            n_symbols = all_correlations.shape[0]
            
            for i in range(n_symbols):
                for j in range(i+1, n_symbols):
                    for k in range(j+1, n_symbols):
                        for l in range(k+1, n_symbols):
                            for m in range(l+1, n_symbols):
                                for n in range(m+1, n_symbols):
                                    # Check all 15 pairwise correlations for 6 symbols
                                    corr12 = all_correlations[i, j]
                                    corr13 = all_correlations[i, k]
                                    corr14 = all_correlations[i, l]
                                    corr15 = all_correlations[i, m]
                                    corr16 = all_correlations[i, n]
                                    corr23 = all_correlations[j, k]
                                    corr24 = all_correlations[j, l]
                                    corr25 = all_correlations[j, m]
                                    corr26 = all_correlations[j, n]
                                    corr34 = all_correlations[k, l]
                                    corr35 = all_correlations[k, m]
                                    corr36 = all_correlations[k, n]
                                    corr45 = all_correlations[l, m]
                                    corr46 = all_correlations[l, n]
                                    corr56 = all_correlations[m, n]
                                    
                                    if (min_corr <= corr12 <= max_corr and 
                                        min_corr <= corr13 <= max_corr and
                                        min_corr <= corr14 <= max_corr and
                                        min_corr <= corr15 <= max_corr and
                                        min_corr <= corr16 <= max_corr and
                                        min_corr <= corr23 <= max_corr and
                                        min_corr <= corr24 <= max_corr and
                                        min_corr <= corr25 <= max_corr and
                                        min_corr <= corr26 <= max_corr and
                                        min_corr <= corr34 <= max_corr and
                                        min_corr <= corr35 <= max_corr and
                                        min_corr <= corr36 <= max_corr and
                                        min_corr <= corr45 <= max_corr and
                                        min_corr <= corr46 <= max_corr and
                                        min_corr <= corr56 <= max_corr):
                                        valid_indices.append((i, j, k, l, m, n))
            
            return valid_indices

        # Convert correlation matrix to numpy array
        corr_array = full_corr_matrix.values
        symbols_array = np.array(symbols)

        # Find valid combinations of different sizes
        valid_combos = []
        for n in matches:
            print(f"Finding valid combinations of size {n}...")
            
            # Use the appropriate function based on the size
            if n == 3:
                indices = check_valid_combinations_of_size_3(corr_array, correlation_max_threshold, correlation_min_threshold)
            elif n == 4:
                indices = check_valid_combinations_of_size_4(corr_array, correlation_max_threshold, correlation_min_threshold)
            elif n == 5:
                indices = check_valid_combinations_of_size_5(corr_array, correlation_max_threshold, correlation_min_threshold)
            elif n == 6:
                indices = check_valid_combinations_of_size_6(corr_array, correlation_max_threshold, correlation_min_threshold)
            else:
                print(f"Size {n} not supported")
                indices = []
            
            # Convert indices back to symbol combinations
            for idx_tuple in indices:
                combo = tuple(str(symbols_array[i]) for i in idx_tuple) # Convert numpy strings to python strings
                valid_combos.append(combo)
            
            print(f"Found {len(indices)} valid combinations of size {n}")

        print(f"Found {len(valid_combos)} valid combinations to process in total")

        # Now continue with your existing code for processing these combinations        
        print(f"Found {len(valid_combos)} valid combinations to process")
        if not valid_combos:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title="No valid combinations found - try relaxing correlation constraints",
                paper_bgcolor='black', 
                plot_bgcolor='black',
                font=dict(color='white')
            )
            # Return empty figures for all plots, including market phase
            return ("No valid combinations found", empty_fig, empty_fig, False, "", empty_fig, None, None, None)#, # MPT/CVaR outputs - empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig) # Market Phase outputs
        
        # Process combinations in parallel
        all_results = []
        num_processes = get_optimal_cores(max_usage_percent=80)
        max_combos_to_process = min(max_combos, len(valid_combos))
        
        print(f"Starting parallel processing with {num_processes} cores")
        print(f"Processing up to {max_combos_to_process} combinations")

        # Implement adaptive batch sizing based on CPU load
        def get_adaptive_batch_size(max_combos_to_process, num_processes):
            """Calculate optimal batch size based on system load"""
            current_cpu = psutil.cpu_percent(interval=1)
            
            # Smaller batches when CPU is high
            if current_cpu > 80:
                factor = 5
            elif current_cpu > 70:
                factor = 4
            elif current_cpu > 60:
                factor = 3
            else:
                factor = 2
                
            return max(1, min(10, max_combos_to_process // (num_processes * factor)))

        # Then use it in your code
        chunk_size = get_adaptive_batch_size(max_combos_to_process, num_processes)

        data_chunks = []

        for i in range(0, max_combos_to_process, chunk_size):
            chunk_end = min(i + chunk_size, max_combos_to_process)
            # Create data for just this chunk with both datasets
            chunk_data = [(combo, returns_df, full_returns_df) for combo in valid_combos[i:chunk_end]]
            data_chunks.append(chunk_data)

        # Process chunks using the global parallel executor
        # Note: n_jobs is now controlled by the global instance configuration
        batch_results = parallel_executor(
            delayed(cached_process_combo)(chunk, frontier_option) for chunk in data_chunks
        )

        # Flatten results
        for result_list in batch_results:
            all_results.extend(result_list)
        
        # Organize results by portfolio type
        for result_type, candidate in all_results:
            if result_type == 'minvar':
                all_portfolios_minvar.append(candidate)
            elif result_type == 'maxsharpe':
                all_portfolios_maxsharpe.append(candidate)
            elif result_type == 'maxsortino':
                all_portfolios_maxsortino.append(candidate)
            elif result_type == 'maxomega':
                all_portfolios_maxomega.append(candidate)
            elif result_type == 'maxcalmar':
                all_portfolios_maxcalmar.append(candidate)
            elif result_type == 'maxmodsharpe':
                all_portfolios_maxmodsharpe.append(candidate)
            elif result_type == 'composite':
                all_candidates_composite.append(candidate)
            elif result_type == 'ss_composite':
                ss_candidates_composite.append(candidate)

        # After organizing results by portfolio type, add this code
        # to track the top return combinations
        top_return_candidates = []

        # Track combinations with high returns regardless of ratios
        for result_type, candidate in all_results:
            # Make a copy and add it to our tracking list
            if 'return' in candidate:
                top_return_candidates.append(candidate.copy())

        # Sort all candidates by return (descending)
        sorted_by_return = sorted(top_return_candidates, key=lambda x: x['return'], reverse=True)

        # Keep just the top 10 highest return combinations
        top_return_candidates = sorted_by_return[:10]

        # Add a special label for these
        for cand in top_return_candidates:
            cand['optimization'] = 'Max Return'

        end_time = datetime.now()
        elapsed = (end_time - start_time).total_seconds()
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Time elapsed: {elapsed:.2f} seconds.")
        print(f"Generated portfolios: MinVar={len(all_portfolios_minvar)}, MaxSharpe={len(all_portfolios_maxsharpe)}, " +
            f"MaxSortino={len(all_portfolios_maxsortino)}, MaxOmega={len(all_portfolios_maxomega)}, " +
            f"MaxCalmar={len(all_portfolios_maxcalmar)}, MaxModSharpe=", len(all_portfolios_maxmodsharpe))
        
        # Sort candidates.
        sorted_minvar = sorted(all_portfolios_minvar, key=lambda x: x['risk'])
        sorted_maxsharpe = sorted(all_portfolios_maxsharpe, key=lambda x: -x['sharpe'])
        #sorted_maxsharpe.reverse()
        sorted_maxsortino = sorted(all_portfolios_maxsortino, key=lambda x: -x['sortino'])
        #sorted_maxsortino.reverse()
        sorted_maxomega = sorted(all_portfolios_maxomega, key=lambda x: -x['omega'])
        #sorted_maxomega.reverse()
        sorted_maxcalmar = sorted(all_portfolios_maxcalmar, key=lambda x: -x['calmar'])
        #sorted_maxcalmar.reverse()
        sorted_maxmodsharpe = sorted(all_portfolios_maxmodsharpe, key=lambda x: -x['mod_sharpe'])

        # IMPORTANT: Create a separate list for the very best performers that will bypass filtering
        top_performers = []

        # Add the top 5 Sharpe ratio portfolios to guaranteed list
        if sorted_maxsharpe:
            top_sharpe = sorted_maxsharpe[:5]
            for cand in top_sharpe:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Sharpe'
                top_performers.append(cand_copy)

        # Add the top 5 Sortino ratio portfolios to guaranteed list
        if sorted_maxsortino:
            top_sortino = sorted_maxsortino[:5]
            for cand in top_sortino:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Sortino'
                top_performers.append(cand_copy)

        # Add the top 5 Omega ratio portfolios to guaranteed list
        if sorted_maxomega:
            top_omega = sorted_maxomega[:5]
            for cand in top_omega:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Omega'
                top_performers.append(cand_copy)

        # Add the top 5 Calmar ratio portfolios to guaranteed list
        if sorted_maxcalmar:
            top_calmar = sorted_maxcalmar[:5]
            for cand in top_calmar:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top Calmar'
                top_performers.append(cand_copy)

        # Add the top 5 Calmar ratio portfolios to guaranteed list
        if sorted_maxmodsharpe:
            top_modsharpe = sorted_maxmodsharpe[:5]
            for cand in top_modsharpe:
                cand_copy = cand.copy()
                cand_copy['optimization'] = 'Top CF Sharpe'
                top_performers.append(cand_copy)

        # Define size-specific pair occurrence thresholds
        pair_occurrence_thresholds = {
            3: 15,
            4: 20,
            5: 30,
            6: 30
        }
        print(f"Using pair occurrence thresholds: {pair_occurrence_thresholds}")

        def filter_candidates_by_pair_occurrence(candidates, max_occurrences_by_size):
            """
            Filters candidates to enforce a maximum occurrence limit for each pair,
            prioritizing higher-returning candidates.
            
            Args:
                candidates: List of portfolio candidates to filter
                max_occurrences_by_size: Dictionary mapping combo sizes (3,4,5) to maximum 
                                        allowed occurrences of any pair in the filtered results
            
            Returns:
                List of filtered candidates that meet the pair occurrence constraints
            """
            if not candidates:
                return []

            # Sort candidates by 'return' (lowercase) descending to prioritize keeping the best ones
            try:
                sorted_candidates = sorted(candidates, key=lambda x: x.get('return', -float('inf')), reverse=True)
            except TypeError:
                # Fallback if 'return' contains non-numeric data
                print("Warning: Could not sort candidates by 'return'. Filtering without prioritization.")
                sorted_candidates = candidates

            filtered_candidates = []
            accepted_pair_counts = {} # Tracks counts of pairs in the filtered list

            for cand in sorted_candidates:
                combo = cand.get('combo', [])
                combo_size = len(combo)

                # Keep candidates not of size 3, 4, or 5 unconditionally
                if combo_size not in max_occurrences_by_size:
                    filtered_candidates.append(cand)
                    continue

                # Check if adding this candidate exceeds any pair limits
                base_symbols = [sym.lstrip('-') for sym in combo]
                can_add = True
                pairs_in_combo = []
                if not base_symbols or len(base_symbols) < 2:
                    can_add = False # Cannot form pairs from empty or single-item combos
                else:
                    max_allowed = max_occurrences_by_size[combo_size]
                    for pair in combinations(base_symbols, 2):
                        sorted_pair = tuple(sorted(pair))
                        pairs_in_combo.append(sorted_pair)
                        if accepted_pair_counts.get(sorted_pair, 0) >= max_allowed:
                            can_add = False
                            break # Exceeds limit for this pair

                # If allowed, add to list and update counts
                if can_add:
                    filtered_candidates.append(cand)
                    for pair in pairs_in_combo:
                        accepted_pair_counts[pair] = accepted_pair_counts.get(pair, 0) + 1

            print(f"Filtered {len(candidates)} -> {len(filtered_candidates)} candidates.")
            return filtered_candidates
        
        # Apply the new pair-based filtering
        print("Applying pair occurrence filter...")
        best_minvar = filter_candidates_by_pair_occurrence(sorted_minvar, pair_occurrence_thresholds)
        best_minvar = best_minvar[:20]
        best_maxsharpe = filter_candidates_by_pair_occurrence(sorted_maxsharpe, pair_occurrence_thresholds)
        best_maxsharpe = best_maxsharpe[:1000]
        best_maxsortino = filter_candidates_by_pair_occurrence(sorted_maxsortino, pair_occurrence_thresholds)
        best_maxsortino = best_maxsortino[:1000]
        best_maxcalmar = filter_candidates_by_pair_occurrence(sorted_maxcalmar, pair_occurrence_thresholds)
        best_maxcalmar = best_maxcalmar[:1000]
        best_maxmodsharpe = filter_candidates_by_pair_occurrence(sorted_maxmodsharpe, pair_occurrence_thresholds)
        best_maxmodsharpe = best_maxmodsharpe[:1000]
        best_maxomega = filter_candidates_by_pair_occurrence(sorted_maxomega, pair_occurrence_thresholds)
        best_maxomega = best_maxomega[:1000]

        # Note: The 'required_count' logic from the old filter is removed.
        # If you need to limit the number of results per category, that logic needs to be added separately after filtering.
        for cand in best_maxomega:
            cand['optimization'] = 'Max Omega'

        # Composite candidates: low min-var and high Sharpe.
        composite_candidates = [cand for cand in all_candidates_composite if 'sharpe' in cand]
        composite_sorted = sorted(composite_candidates, key=lambda x: x['sharpe']/(x['risk']+1e-15), reverse=True)
        best_composite = filter_candidates_by_pair_occurrence(composite_sorted, pair_occurrence_thresholds)
        best_composite = best_composite[:20]
        for cand in best_composite:
            cand['optimization'] = 'High Sharpe & Low MinVar'
        
        # Composite candidates based on both Sharpe and Sortino ratios.
        ss_composite_candidates = [cand for cand in ss_candidates_composite if 'sharpe' in cand and 'sortino' in cand]
        
        # Check if any candidate exists before computing max values.
        if ss_composite_candidates:
            # Define weights for Sharpe and Sortino contributions.
            sharpe_weight = 0.5  # Adjust based on preference
            sortino_weight = 0.5

            # Normalize Sharpe and Sortino ratios.
            max_sharpe_value = max(cand['sharpe'] for cand in ss_composite_candidates) + 1e-15
            max_sortino_value = max(cand['sortino'] for cand in ss_composite_candidates) + 1e-15
        
            # Compute combined composite score ("MaxSS") using both weights.
            maxss_candidates = sorted(
                ss_composite_candidates,
                key=lambda x: ((x['sharpe'] / max_sharpe_value) * sharpe_weight + (x['sortino'] / max_sortino_value) * sortino_weight) / ((abs(x['risk']) + 1e-15)),
                reverse=True
            )
        
            # Select top candidates and update their optimization type.
            #best_maxss = maxss_candidates[:20]
            best_maxss = filter_candidates_by_pair_occurrence(maxss_candidates, pair_occurrence_thresholds)
            best_maxss = best_maxss[:20]
            for cand in best_maxss:
                cand['optimization'] = 'MaxSS'
        else:
            best_maxss = []

        # Combine candidates in the desired order: 5 min-var, then 10 composite, then 20 max-sharpe.
        final_candidates = top_performers + best_minvar + best_composite + best_maxsharpe + best_maxsortino + best_maxomega + best_maxcalmar + best_maxmodsharpe + best_maxss
        
        # For visualization only
        visualization_candidates = final_candidates + top_return_candidates
        
        # Determine the selected candidate (if a dot was clicked) and its marker color.
        def get_marker_color(opt):
            if opt == 'High Sharpe & Low MinVar':
                return 'orange'
            elif opt == 'Min Variance':
                return 'cyan'
            elif opt == 'Max Sortino':
                return 'yellow'
            elif opt == 'MaxSS':
                return 'red'
            elif opt == 'Max Omega':
                return 'green'
            elif opt == 'Max Calmar':
                return 'dodgerblue'
            elif opt == 'Max CF Sharpe':
                return 'violet'
            else:
                return 'magenta'
        
        selected_candidate = None
        row_highlight_color = None
        
        if clickData and 'points' in clickData and clickData['points']:
            try:
                customdata_str = clickData['points'][0]['customdata']
                selected_candidate = json.loads(customdata_str)
                row_highlight_color = get_marker_color(selected_candidate.get("optimization"))
            except Exception as e:
                print(f"Error getting customdata and selected candidates (table): {e}")
                pass
        
        # Build the HTML table.
        table_header = [
            html.Tr([
                html.Th("Type"),
                html.Th("Symbols"),
                html.Th("Return"),
                html.Th("Risk"),
                html.Th("VaR 95%"),
                html.Th("CVaR 95%"),
                html.Th("DRisk"),
                html.Th("Sharpe"),
                html.Th("Sortino"),
                html.Th("Omega"),
                html.Th("Calmar"),
                html.Th("CF Sharpe"),
                html.Th("Martin"),
                html.Th("UI"),
                html.Th("Pain R."),
                html.Th("Weights")
            ])
        ]
        table_rows = []
        '''
        for port in final_candidates: # Use final_candidates which includes filtered results
            row_style = {}
            if selected_candidate and port.get('combo') == selected_candidate.get("combo"):
                row_style = {'backgroundColor': row_highlight_color, 'color': 'black'}

            # Safely get values using .get() with default 0
            row = html.Tr([
                html.Td(port.get('optimization', 'N/A')),
                html.Td(", ".join(port.get('combo', []))),
                html.Td(f"{port.get('return', 0):.4f}"),
                html.Td(f"{port.get('risk', 0):.4f}"),
                html.Td(f"{port.get('var_95', 0):.4f}"),
                html.Td(f"{port.get('cvar_95', 0):.4f}"),
                html.Td(f"{port.get('drisk', 0):.4f}"),
                html.Td(f"{port.get('sharpe', 0):.4f}"),
                html.Td(f"{port.get('sortino', 0):.4f}"),
                html.Td(f"{port.get('omega', 0):.4f}"),
                html.Td(f"{port.get('calmar', 0):.4f}"),
                html.Td(f"{port.get('mod_sharpe', 0):.4f}"),
                html.Td(f"{port.get('martin', 0):.4f}"), # Martin Ratio
                html.Td(f"{port.get('ulcer_index', 0):.4f}"), # Ulcer Index
                html.Td(f"{port.get('pain_ratio', 0):.4f}"), # Pain Ratio
                html.Td(", ".join([f"{sym}:{w:.2%}" for sym, w in zip(port.get('combo', []), port.get('weights', []))]))
            ], style=row_style)
            table_rows.append(row)
        # Assign the generated table to table_content
        '''
        table_content = html.Table(table_header + table_rows, style={'border': '1px solid white', 'width': '100%'})
        
        # Build the Efficient Frontier chart.
        frontier_fig = go.Figure()
        
        # --- Compute Efficient Frontier Line ---
        # Here we compute efficient frontier points over the entire universe (all 28 symbols)
        # using the full returns data. This is similar in spirit to PyPortfolioOpt's approach.
        # Convert log returns to arithmetic returns for portfolio optimization
        arithmetic_returns_all = convert_log_to_arithmetic_returns(returns_df)
        mean_returns_all = arithmetic_returns_all.mean()
        cov_matrix_all = arithmetic_returns_all.cov()
        n_assets_all = len(mean_returns_all)
        init_guess_all = np.repeat(1/n_assets_all, n_assets_all)
        bounds_all = tuple((-1, 1) for _ in range(n_assets_all))
        base_constraints = [{'type':'eq', 'fun': lambda w: np.sum(w)-1}]

        # Compute global minimum variance portfolio.
        res_min_all = sco.minimize(portfolio_variance, init_guess_all,
                                args=(cov_matrix_all,),
                                method='SLSQP', bounds=bounds_all, constraints=base_constraints)
        if res_min_all.success:
            w_min_all = res_min_all.x
            ret_min = np.dot(w_min_all, mean_returns_all)
            risk_min = np.sqrt(portfolio_variance(w_min_all, cov_matrix_all))
        else:
            ret_min = mean_returns_all.min()
            risk_min = None

        ret_max = mean_returns_all.max()
        
        #@memory.cache
        @timing_decorator
        def efficient_frontier_points(mean_returns, cov_matrix, num_points=50):
            frontier = []
            previous_solution = w_min_all.copy()
            targets = np.linspace(ret_min, ret_max, num_points)
            for target in targets:
                constraints_target = base_constraints + [{
                    'type': 'eq',
                    'fun': lambda w, target=target: np.dot(w, mean_returns) - target
                }]
                res = sco.minimize(
                    portfolio_variance,
                    previous_solution,
                    args=(cov_matrix,),
                    method='SLSQP',
                    bounds=bounds_all,
                    constraints=constraints_target,
                    options={'ftol': 1e-9}
                )
                if res.success:
                    risk_val = np.sqrt(portfolio_variance(res.x, cov_matrix))
                    frontier.append((risk_val, target))
                    previous_solution = res.x  # warm-start for the next iteration
            if risk_min is not None and (risk_min, ret_min) not in frontier:
                frontier.insert(0, (risk_min, ret_min))
            return frontier

        frontier_pts = efficient_frontier_points(mean_returns_all, cov_matrix_all, num_points=28)
        if frontier_pts:
            risks, rets = zip(*frontier_pts)
            frontier_fig.add_trace(go.Scatter(
                x=risks,
                y=rets,
                mode='lines',
                line=dict(color='magenta', width=2, dash='dot'),
                name='Efficient Frontier (28 pairs)'
            ))
            
        # Create candidate groups based on your optimization type:
        # Group 1: Min Variance + Composite + Max Sharpe (original frontier)
        group1 = best_maxsharpe
        # Group 2: Max Sortino candidates
        group2 = best_maxsortino
        # Group 3: Max Omega candidates
        group3 = best_maxomega
        # Group 4: Max Calmar candidates
        group4 = best_maxcalmar
        # Group 5: Combined frontier for all candidates
        group5 = best_minvar + best_composite + best_maxsharpe + best_maxsortino + best_maxomega + best_maxcalmar + best_maxmodsharpe + best_maxss
        # Group 6: Max CF Sharpe candidates
        group6 = best_maxmodsharpe

        def efficient_frontier_upper_hull(candidates):
            points = [(c['risk'], c['return']) for c in candidates]
            if len(points) < 2:
                return points
            
            # Sort by risk ascending, then return ascending
            points = sorted(points, key=lambda x: (x[0], x[1]))
            
            def cross(o, a, b):
                """Cross product of OA x OB > 0 => counter-clockwise turn."""
                return (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
            
            # Build lower hull (we won't actually need it, but let's keep for completeness)
            lower = []
            for p in points:
                while len(lower) >= 2 and cross(lower[-2], lower[-1], p) <= 0:
                    lower.pop()
                lower.append(p)
            
            # Build upper hull
            upper = []
            for p in reversed(points):
                while len(upper) >= 2 and cross(upper[-2], upper[-1], p) <= 0:
                    upper.pop()
                upper.append(p)
            
            # Concatenate lower + upper hull to get the full polygon
            # but we only want the top edge.  By convention, the “upper”
            # list (minus its last point) goes left->right across the top.
            # In many “efficient frontier” plots, you only need `upper`.
            # Typically, the last point in `upper` is the same as the first
            # in `lower`, so we slice them out.
            # If you literally only want the top boundary (no lower hull),
            # just return the “upper” chain in left-to-right order:
            frontier = list(reversed(upper))[1:]  # skip the last to avoid duplication
            return frontier

        #@memory.cache
        @timing_decorator
        def compute_all_hulls_parallel(groups):
            """Compute convex hulls for multiple portfolio groups in parallel with CPU limiting"""
            
            # Calculate optimal cores based on current CPU usage
            #num_cores = min(6, get_optimal_cores(max_usage_percent=80))
            
            # Use the global parallel executor
            # Note: n_jobs is now controlled by the global instance configuration
            results = parallel_executor(
                delayed(efficient_frontier_upper_hull)(group) for group in groups
            )
            
            return results

        # Use the parallelized function
        groups = [group1, group2, group3, group4, group5, group6]
        group_names = ['MaxSharpe', 'MaxSortino', 'MaxOmega', 'MaxCalmar', 'Full', 'MaxCFSharpe']
        group_colors = ['magenta', 'yellow', 'green', 'dodgerblue', 'white', 'violet']
        group_widths = [2, 2, 2, 2, 4, 2]

        all_hulls = compute_all_hulls_parallel(groups)

        # Add traces for all groups
        for hull, name, color, width in zip(all_hulls, group_names, group_colors, group_widths):
            if hull:
                rx, ry = zip(*hull)
                frontier_fig.add_trace(
                    go.Scatter(
                        x=rx, y=ry, mode='lines',
                        line=dict(color=color, width=width),
                        name=f'Frontier ({name})'
                    )
                )

        # --- Build candidate dots on the Efficient Frontier Chart ---

        # Add this function after preparing all candidates but before adding traces to frontier_fig
        def get_optimization_priority(opt_type):
            """Return priority value for optimization types (lower value = higher priority = appears on top)"""
            priority_map = {
                "Max Sharpe": 1,
                "Top Sharpe": 1,
                "Max Calmar": 2, 
                "Top Calmar": 2,
                "Max Sortino": 3,
                "Top Sortino": 3,
                "Max Omega": 4,
                "Top Omega": 4,
                "MaxSS": 5,
                "High Sharpe & Low MinVar": 6,
                "Min Variance": 7,
                "Max Return": 8,  # Can adjust this priority as needed
                "Max CF Sharpe": 9, 
                "Top CF Sharpe": 9
            }
            return priority_map.get(opt_type, 9)  # Default priority for unlisted types
        
        all_visualization_candidates = sorted(
            visualization_candidates,
            key=lambda x: get_optimization_priority(x.get('optimization', ''))
        )

        # Now add dots for the individual candidates (those not in the three groups).
        for port in all_visualization_candidates:
            # Check if this is a top performer
            is_top_performer = port['optimization'].startswith('Top ')
            is_top_return = port['optimization'] == 'Max Return'
            
            # Set marker symbol based on candidate type
            if is_top_return:
                marker_symbol = 'x'  # Use x symbol for top return candidates
                marker_size = 10     # Make them slightly larger
            elif is_top_performer:
                marker_symbol = 'asterisk'
            else:
                marker_symbol = 'circle'

            # Determine marker color as in the original logic.
            if port['optimization'] == 'Max Return':
                marker_color = 'blue'  # Different color for top return
            if port['optimization'] == 'High Sharpe & Low MinVar':
                marker_color = 'orange'
            elif port['optimization'] == 'Min Variance':
                marker_color = 'cyan'
            elif port['optimization'] == 'MaxSS':
                marker_color = 'red'
            elif port['optimization'] == 'Max CF Sharpe':
                marker_color = 'violet'
            else:
                marker_color = (
                    'magenta' if port['optimization'] == 'Max Sharpe' or port['optimization'] == 'Top Sharpe'
                    else 'yellow' if port['optimization'] == 'Max Sortino' or port['optimization'] == 'Top Sortino'
                    else 'green' if port['optimization'] == 'Max Omega' or port['optimization'] == 'Top Omega'
                    else 'dodgerblue' if port['optimization'] == 'Max Calmar' or port['optimization'] == 'Top Calmar'
                    else 'gray'
                )
            # Override if candidate is clicked.
            if selected_candidate and port['combo'] == selected_candidate.get("combo"):
                marker_color = "white"
                marker_size = 14
            else:
                marker_size = 10
            #total = sum(port['weights'])
            total = sum(abs(w) for w in port['weights'])
            normalized_weights = ", ".join(f"{w/total:.2f}" for w in port['weights'])
            tooltip = json.dumps({
                "combo": port['combo'],
                "weights": port['weights'],
                "optimization": port['optimization'],
                "risk": port.get('risk', 0), # Add risk
                "return": port.get('return', 0), # Add return
                "cvar_95": port.get('cvar_95', 0), # Add cvar_95
                "sharpe": port.get('sharpe', 0),
                "sortino": port.get('sortino', 0),
                "omega": port.get('omega', 0),
                "calmar": port.get('calmar', 0),
                "mod_sharpe": port.get('mod_sharpe', 0)
            })
            frontier_fig.add_trace(go.Scatter(
                x=[port['risk']],
                y=[port['return']],
                mode='markers',
                marker=dict(
                    size=12 if is_top_performer or is_top_return else 10,  # Larger size for top performers
                    color=marker_color,
                    symbol=marker_symbol,  # Use the symbol we determined above
                    line=dict(width=2, color='white') if is_top_performer or is_top_return else None  # Add white outline for crosses
                ),
                customdata=[tooltip],
                meta=normalized_weights,
                hovertemplate=(
                    "Risk: %{x:.4f}<br>" +
                    "Return: %{y:.4f}<br>" +
                    "Symbols: " + ", ".join(port['combo']) + "<br>" +
                    "Weights: %{meta}<br>" +
                    "Optimization: " + port['optimization'] + "<br>" +
                    "Sharpe: " + f"{port.get('sharpe', 0):.4f}" + "<br>" +
                    "Sortino: " + f"{port.get('sortino', 0):.4f}" + "<br>" +
                    "Omega: " + f"{port.get('omega', 0):.4f}" + "<br>" +
                    "Calmar: " + f"{port.get('calmar', 0):.4f}" + "<br>" +
                    "CF Sharpe: " + f"{port.get('mod_sharpe', 0):.4f}" + "<br>" +
                    "Ulcer Index: " + f"{port.get('ulcer_index', 0):.4f}" + "<br>" +
                    "Martin Ratio: " + f"{port.get('martin', 0):.4f}" + "<br>" +
                    "Pain Ratio: " + f"{port.get('pain_ratio', 0):.4f}" + "<br>" +
                    "VaR 95%: " + f"{port.get('var_95', 0):.4f}" + "<br>" +
                    "CVaR 95%: " + f"{port.get('cvar_95', 0):.4f}<extra></extra>"
                )
            ))

        # --- Add main pair dots as small gray stars
        # Use the main symbols list (28 pairs) to mark their individual risk and return.
        main_pairs = symbols  # 'symbols' was defined earlier as the full list of main pairs.
        for sym in main_pairs:
            if sym in returns_df.columns:
                # For a single symbol, we can use its standard deviation as risk
                # and its mean return as expected return.
                # Convert log returns to arithmetic returns for portfolio metrics
                arithmetic_sym_returns = convert_log_to_arithmetic_returns(returns_df[sym])
                risk_val = arithmetic_sym_returns.std()
                raw_return = arithmetic_sym_returns.mean()
                return_val = abs(arithmetic_sym_returns.mean())
                
                # Format the display symbol with negative prefix if needed
                display_sym = f"-{sym}" if raw_return < 0 else sym

                frontier_fig.add_trace(go.Scatter(
                    x=[risk_val],
                    y=[return_val],
                    mode='markers',
                    marker=dict(symbol='star', size=8, color='gray'),
                    name=f'{sym} (Main Pair)',
                    hovertemplate=f"{display_sym}: Risk: %{{x:.4f}}<br>Return: %{{y:.4f}}<extra></extra>"
                ))

        # Compute max risk value from max Sharpe candidates.
        if final_candidates:
            max_sharpe_risk = max(port['risk'] for port in final_candidates)
        else:
            max_sharpe_risk = 0.001
        # Add this code to calculate max_cvar early:
        if final_candidates:
            max_cvar = max(port.get('cvar_95', 0.001) for port in final_candidates)
        else:
            max_cvar = 0.001

        # Find recommended portfolios for standard frontier
        best_returns, best_metric, best_hedge, mid_frontier = find_recommended_portfolios(
            visualization_candidates, returns_df, 
            all_hulls[4],  # Using the "Full" hull (index 4)
            type='risk'
        )

        recommended_portfolios = [best_returns, best_metric, best_hedge, mid_frontier]
        
        frontier_fig.update_layout(
            title="Efficient Frontier",
            xaxis_title="Risk (Std. Dev.)",
            #xaxis=dict(range=[0, max_sharpe_risk + 10]),
            xaxis=dict(
                range=[0, max_sharpe_risk + 0.0001],
                #autorange=True,  # Enable autorange to adapt to your data
                showgrid=True,   # Show grid lines
                gridcolor='rgba(100, 100, 100, 0.2)'  # Subtle grid
            ),
            yaxis_title="Return",
            #width=2000,  # adjust width (2x default, for example)
            height=1000,  # adjust height (2x default, for example)
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white'),
            updatemenus=[
                dict(
                    type="buttons",
                    direction="right",
                    buttons=[
                        dict(
                            args=[{"xaxis.autorange": True, "yaxis.autorange": True}],
                            label="Auto Range",
                            method="relayout"
                        ),
                        dict(
                            args=[{"xaxis.autorange": False, "xaxis.range": [0, max_sharpe_risk], 
                                "yaxis.autorange": True}],
                            label="Focus Risk",
                            method="relayout"
                        )
                    ],
                    pad={"r": 10, "t": 10},
                    showactive=False,
                    x=0.15,
                    y=1.15,
                    xanchor="left",
                    yanchor="top",
                    bgcolor="rgba(60, 60, 60, 0.7)",
                    bordercolor="rgba(200, 200, 200, 0.5)"
                )
            ]
        )

        # After computing max_sharpe_risk and updating frontier_fig layout:
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.90 * max_sharpe_risk,
            x1=0.90 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="red", dash="dot", width=2),
            name="90%"
        )
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.80 * max_sharpe_risk,
            x1=0.80 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="aqua", dash="dot", width=2),
            name="80%"
        )
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.60 * max_sharpe_risk,
            x1=0.60 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="lime", dash="dot", width=2),
            name="60%"
        )
        frontier_fig.add_shape(
            type="line",
            xref="x",
            yref="paper",
            x0=0.40 * max_sharpe_risk,
            x1=0.40 * max_sharpe_risk,
            y0=0,
            y1=1,
            line=dict(color="yellow", dash="dot", width=2),
            name="40%"
        )

        # Build the portfolio return and quadrant charts if a dot is clicked.
        portfolio_returns_fig = go.Figure()
        if clickData and 'points' in clickData and clickData['points']:
            try:
                # First check if customdata exists and is a valid format
                customdata_raw = clickData['points'][0].get('customdata')
                
                if customdata_raw is None:
                    raise ValueError("No customdata available in the clicked point")
                    
                # Handle both string and object formats of customdata
                if not isinstance(customdata_raw, (str, bytes, bytearray)):
                    customdata_str = json.dumps(customdata_raw)
                else:
                    customdata_str = customdata_raw
                
                # Try parsing the JSON string
                port_info = json.loads(customdata_str)
                
                # Check if this is a combined portfolio and handle accordingly
                if port_info.get('is_combined_portfolio') and 'source_portfolios' in port_info:
                    print("Processing combined portfolio from button")
                    source_portfolios = port_info['source_portfolios']
                    
                    # Filter out any None portfolios
                    source_portfolios = [p for p in source_portfolios if p is not None]
                    
                    if source_portfolios:
                        # Apply equal weighting to all valid portfolios
                        weights = [0.25] * len(source_portfolios)  # Default to 25% each
                        if len(source_portfolios) != 4:  # If we don't have exactly 4 portfolios
                            weights = [1.0/len(source_portfolios)] * len(source_portfolios)  # Equal weights
                        
                        # Combine the portfolios
                        combined_portfolio = combine_portfolios(source_portfolios, weights)
                        combined_portfolio['optimization'] = "Combined Portfolio"
                        
                        # Replace port_info with our combined portfolio
                        port_info = combined_portfolio
                        print(f"Successfully combined {len(source_portfolios)} portfolios")
                
                # Verify required fields exist
                if "combo" not in port_info or "weights" not in port_info:
                    raise ValueError("Missing required fields in customdata")
                
                selected_combo = port_info["combo"]
                selected_weights = port_info["weights"]
            
            except Exception as e:
                print(f"Error processing clickData: {e}")
                # Create default figures with error message
                portfolio_returns_fig = go.Figure()
                portfolio_returns_fig.update_layout(
                    title=f"Error processing portfolio: {str(e)}",
                    paper_bgcolor='black',
                    plot_bgcolor='black',
                    font=dict(color='white')
                )
                # Return default/empty values for ALL outputs on error
                return (html.Div(f"Error processing click data: {e}"), empty_fig, portfolio_returns_fig, False, "", empty_fig, None, None, None)#, # MPT/CVaR outputs - empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig) # Market Phase outputs
            
            # Convert each displayed symbol (with optional '-' prefix) to base symbol with positive/negative weight
            base_symbols = []
            adjusted_weights = []
            
            for sym, weight in zip(selected_combo, selected_weights):
                base_symbols.append(sym)
                adjusted_weights.append(weight)

            # Build the candidate portfolio series.
            # During weekends, use full_returns_df for portfolio chart to show complete Friday data
            portfolio_data_source = full_returns_df if should_use_friday_data() else returns_df
            subset_returns = portfolio_data_source[base_symbols]
            port_return_series = pd.Series(0, index=subset_returns.index)
            for base, weight in zip(base_symbols, adjusted_weights):
                port_return_series += subset_returns[base] * weight

            # Compute cumulative returns.
            cum_returns = port_return_series.cumsum()
            # Filter out weekend dates.
            cum_returns = cum_returns[cum_returns.index.dayofweek < 5]

            # Convert index to timezone-naive local time for correct chart display
            cum_returns.index = cum_returns.index.tz_convert('Europe/Bucharest').tz_localize(None)
            
            # Plot the portfolio cumulative returns chart.
            portfolio_returns_fig.add_trace(go.Scatter(
                x=cum_returns.index,
                y=cum_returns,
                mode='lines',
                line=dict(color='white', width=3),
                name="Cumulative Returns"
            ))
            portfolio_returns_fig.update_layout(
                title="Portfolio Cumulative Returns",
                xaxis_title="Time",
                yaxis_title="Cumulative Return",
                paper_bgcolor='black',
                plot_bgcolor='black',
                #width=2000,  # adjust width (2x default, for example)
                height=1000,  # adjust height (2x default, for example)
                font=dict(color='white')
            )
        
            # Assuming portfolio_cum_returns holds the cumulative returns series
            current_value = cum_returns.iloc[-1]
            portfolio_returns_fig.add_shape(
                type="line",
                x0=cum_returns.index[0],
                y0=current_value,
                x1=cum_returns.index[-1],
                y1=current_value,
                line=dict(color="red", dash="dash", width=2),
                xref="x",
                yref="y"
            )
            # Add an annotation showing the last y-axis value at the right-end of the line.
            portfolio_returns_fig.add_annotation(
                x=cum_returns.index[-1],
                y=current_value,
                xref="x",
                yref="y",
                text=f"{current_value:.4f}",
                xanchor="left",
                yanchor="bottom",
                showarrow=False,
                font=dict(color="red", size=12),
                bgcolor="black"
            )

            # After you process the clicked portfolio
            try:
                # Define scale_total here to ensure it's available
                scale_total = annotation_total_weight if annotation_total_weight is not None else 0.20
                
                # Determine which frontier was clicked based on stored data or trigger_id
                frontier_type = None
                if clicked_portfolio_data_to_store and 'frontier_type' in clicked_portfolio_data_to_store:
                    frontier_type = clicked_portfolio_data_to_store['frontier_type']
                elif trigger_id == 'efficient-frontier':
                     frontier_type = 'efficient-frontier'
                elif trigger_id == 'cvar-frontier':
                     frontier_type = 'cvar-frontier'

                # Generate suggestions based on the determined frontier type
                # Use port_info directly, as it contains the data from the actual clicked point's customdata
                if frontier_type == 'efficient-frontier':
                    try:
                        # Ensure port_info (parsed from customdata earlier) is used as the clicked_portfolio
                        clicked_portfolio_from_click = port_info # Use the parsed data

                        # Ensure clicked_portfolio has all required fields (using the data from the click)
                        clicked_portfolio_from_click['risk'] = clicked_portfolio_from_click.get('risk', 0.0001)
                        clicked_portfolio_from_click['return'] = clicked_portfolio_from_click.get('return', 0)
                        clicked_portfolio_from_click['cvar_95'] = clicked_portfolio_from_click.get('cvar_95', 0)

                        print(f"--- Debug EF: Using clicked_portfolio from clickData: {clicked_portfolio_from_click['combo']}") # Add debug print

                        # Generate portfolio suggestions using the correct clicked portfolio data
                        suggested_portfolios = generate_portfolio_suggestions(
                            clicked_portfolio_from_click, visualization_candidates, max_sharpe_risk
                        )

                        # Combine clicked portfolio with suggested portfolios
                        all_portfolios = [clicked_portfolio_from_click] + suggested_portfolios # Use correct variable
                        # Equal weights for all portfolios
                        weights = [1.0 / len(all_portfolios)] * len(all_portfolios)

                        # Combine using the function from func_portfolio (or gfx_funcs if it's there)
                        combined_portfolio = combine_portfolios(all_portfolios, weights)

                        # MPT string will be created after suggestion generation succeeds
                    except Exception as e:
                        print("Error processing Efficient Frontier click:", e)
                        # raise dash.exceptions.PreventUpdate # Comment out or handle differently if needed
                # Use port_info directly, as it contains the data from the actual clicked point's customdata
                elif frontier_type == 'cvar-frontier':
                    try: # Outer try for the whole CVaR block
                        # Ensure port_info (parsed from customdata earlier) is used as the clicked_portfolio
                        clicked_portfolio_from_click = port_info # Use the parsed data
                        # Mark this as using CVaR for risk measure in suggestion generation
                        clicked_portfolio_from_click['use_cvar'] = True

                        # Ensure clicked_portfolio has all required fields (using the data from the click)
                        clicked_portfolio_from_click['risk'] = clicked_portfolio_from_click.get('risk', 0.0001)
                        clicked_portfolio_from_click['return'] = clicked_portfolio_from_click.get('return', 0)
                        clicked_portfolio_from_click['cvar_95'] = clicked_portfolio_from_click.get('cvar_95', 0)

                        print(f"--- Debug CVaR: Using clicked_portfolio from clickData: {clicked_portfolio_from_click['combo']}") # Add debug print

                        # Generate portfolio suggestions using the correct clicked portfolio data
                        suggested_portfolios = generate_portfolio_suggestions(
                            clicked_portfolio_from_click, visualization_candidates, max_cvar
                        )

                        # Combine clicked portfolio with suggested portfolios
                        all_portfolios = [clicked_portfolio_from_click] + suggested_portfolios # Use correct variable
                        # Equal weights for all portfolios
                        weights = [1.0 / len(all_portfolios)] * len(all_portfolios)

                        # Combine using the function from func_portfolio (or gfx_funcs if it's there)
                        combined_portfolio = combine_portfolios(all_portfolios, weights)

                        # MPT string will be created after suggestion generation succeeds
                    except Exception as e: # This except corresponds to the inner CVaR try
                         print(f"Error processing CVaR Frontier click: {e}")
                         print(f"Error processing CVaR Frontier click: {e}")
                         # Keep suggested_portfolios as potentially empty list

                # --- Generate MPT String (only if suggestions were generated successfully) ---
                # This block is now outside the EF/CVaR specific try blocks, but inside the main suggestion try block
                try:
                    # Ensure port_info and suggested_portfolios are valid before creating the string
                    if port_info and 'combo' in port_info and 'weights' in port_info: # Check port_info validity
                         combined_mpt_string = create_combined_mpt_string(port_info, suggested_portfolios)
                    else:
                         print("Skipping MPT string creation due to invalid port_info.")
                         combined_mpt_string = "" # Ensure it's empty if port_info is bad
                except Exception as mpt_e:
                    print(f"Error creating MPT string: {mpt_e}")
                    combined_mpt_string = "" # Reset on MPT string creation error

                # --- Add Annotations ---
                # Add annotation for suggestions to portfolio_returns_fig
                if suggested_portfolios:
                    suggestion_text = "Suggested Additional Portfolios:<br><br>"

                    for i, suggestion in enumerate(suggested_portfolios):
                        # Format normalized weights
                        total_weight = sum(abs(w) for w in suggestion['weights'])
                        # Ensure scale_total is defined (it's defined earlier around line 1383)
                        normalized_weights = [(w/total_weight)*scale_total for w in suggestion['weights']]
                        symbols_with_weight = ", ".join(f"{sym}: {w:.2f}" for sym, w in zip(suggestion['combo'], normalized_weights))

                        suggestion_text += (
                            f"Suggestion {i+1} ({suggestion['risk_description']}):<br>"
                            f"{'Risk (CVaR 95%)' if suggestion.get('used_risk_measure') == 'cvar_95' else 'Risk (Std.Dev.)'}: {suggestion.get(suggestion.get('used_risk_measure', 'risk'), 0):.4f} / Return: {suggestion.get('return', 0):.4f}<br>"
                            f"Sharpe: {suggestion.get('sharpe', 0):.4f} / Sortino: {suggestion.get('sortino', 0):.4f} / Omega: {suggestion.get('omega', 0):.4f} / Calmar: {suggestion.get('calmar', 0):.4f} / CF Sharpe: {suggestion.get('mod_sharpe', 0):.4f}<br>"
                            f"Symbols: {symbols_with_weight}<br>"
                            f"MPT Input: {suggestion['mpt_string']}<br><br>"
                        )

                    # Add the suggestions annotation
                    portfolio_returns_fig.add_annotation(
                        x=0.01,
                        y=0.15,  # Position at bottom left
                        xref="paper",
                        yref="paper",
                        text=suggestion_text,
                        align="left",
                        showarrow=False,
                        font=dict(color="white", size=14),
                        bordercolor="white",
                        borderwidth=1,
                        bgcolor="rgba(0,0,0,0.8)"
                    )
            except Exception as e: # This except now catches errors from suggestion generation OR MPT string creation
                print(f"Error during suggestion or MPT string generation: {e}")
                # Optional: Add an annotation about the error
                portfolio_returns_fig.add_annotation(
                    x=0.01,
                    y=0.15,
                    xref="paper",
                    yref="paper",
                    text=f"Error generating suggestions/MPT string: {str(e)}",
                    align="left",
                    showarrow=False,
                    font=dict(color="red", size=14),
                    bordercolor="white",
                    borderwidth=1,
                    bgcolor="rgba(0,0,0,0.8)"
                )
                # Ensure combined_mpt_string is empty on error
                combined_mpt_string = ""

            # Remove weekend gaps on the x-axis.
            portfolio_returns_fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])])
                        
            # After computing candidate portfolio series, add annotation to portfolio_returns_fig.
            # --- Get risk/return values reliably from port_info ---
            if port_info.get('frontier_type') == 'cvar-frontier':
                # Use CVaR if CVaR frontier was the source
                risk_val = port_info.get('cvar_95', 0)
                risk_key_for_display = 'CVaR 95%'
            else:
                # Default to standard risk (Std.Dev.)
                risk_val = port_info.get('risk', 0)
                risk_key_for_display = 'Risk (Std.Dev.)'
            return_val = port_info.get('return', 0)
            # --- End reliable value retrieval ---

            # Get optimization, sharpe and sortino from the candidate info.
            optimization = port_info.get("optimization", "N/A")
            sharpe = port_info.get("sharpe", 0)
            sortino = port_info.get("sortino", 0)
            omega = port_info.get("omega", 0)
            calmar = port_info.get("calmar", 0)
            cfsharpe = port_info.get("mod_sharpe", 0)
            # Compute normalized weights scaled so that their total is annotation_total_weight
            total_candidate_weight = sum(abs(w) for w in selected_weights)
            scale_total = annotation_total_weight if annotation_total_weight is not None else 0.20
            normalized_weights = [(w/total_candidate_weight)*scale_total for w in selected_weights]
            symbols_with_weight = ", ".join(f"{sym}: {w:.2f}" for sym, w in zip(selected_combo, normalized_weights))

            annotation_text = (
                f"{risk_key_for_display}: {risk_val:.4f}<br>" # Use dynamic risk key label
                f"Return: {return_val:.4f}<br>"
                f"Optimization: {optimization}<br>"
                f"Sharpe: {sharpe:.4f} / Sortino: {sortino:.4f} / Omega: {omega:.4f} / Calmar: {calmar:.4f} / CF Sharpe: {cfsharpe:.4f}<br>"
                f"Symbols: {symbols_with_weight}"
            )
            print(f"DEBUG: Annotation text for main portfolio: {annotation_text}") # Add print statement
            portfolio_returns_fig.add_annotation(
                x=0.01,
                y=0.99,
                xref="paper",
                yref="paper",
                text=annotation_text,
                showarrow=False,
                font=dict(color="white", size=14),
                bordercolor="white",
                borderwidth=1,
                bgcolor="black"
            )
        else:
            portfolio_returns_fig.update_layout(
                title="Portfolio Cumulative Returns (Click a dot in the Efficient Frontier)",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            )
            portfolio_returns_fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])])

        # Create a new figure for CVaR frontier
        cvar_frontier_fig = go.Figure()
        
        # Create candidate groups for CVaR frontier using the same categorization logic but sorted by CVaR
        cvar_group1 = sorted(best_maxsharpe, key=lambda x: x.get('cvar_95', 0))
        cvar_group2 = sorted(best_maxsortino, key=lambda x: x.get('cvar_95', 0))
        cvar_group3 = sorted(best_maxomega, key=lambda x: x.get('cvar_95', 0))
        cvar_group4 = sorted(best_maxcalmar, key=lambda x: x.get('cvar_95', 0))
        cvar_group5 = sorted(best_minvar + best_composite + best_maxsharpe + best_maxsortino +
                        best_maxomega + best_maxcalmar + best_maxmodsharpe + best_maxss, key=lambda x: x.get('cvar_95', 0))
        cvar_group6 = sorted(best_maxmodsharpe, key=lambda x: x.get('cvar_95', 0))

        # Create a CVaR-specific efficient frontier hull function
        def efficient_frontier_upper_hull_cvar(candidates):
            # Similar to the original, but using CVaR instead of risk
            # Similar to the original, but using CVaR instead of risk
            points = [(c.get('cvar_95', 0), c['return']) for c in candidates]
            if len(points) < 2:
                return points
            
            # Sort by CVaR ascending, then return ascending
            points = sorted(points, key=lambda x: (x[0], x[1]))
            
            def cross(o, a, b):
                """Cross product of OA x OB > 0 => counter-clockwise turn."""
                return (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
            
            # Build upper hull
            upper = []
            for p in reversed(points):
                while len(upper) >= 2 and cross(upper[-2], upper[-1], p) <= 0:
                    upper.pop()
                upper.append(p)
            
            frontier = list(reversed(upper))[1:]  # skip the last to avoid duplication
            return frontier
        
        # Compute CVaR hulls in parallel
        cvar_groups = [cvar_group1, cvar_group2, cvar_group3, cvar_group4, cvar_group5, cvar_group6]

        # Use the global parallel executor for CVaR hulls
        # Note: n_jobs is now controlled by the global instance configuration
        cvar_hulls = parallel_executor(
            delayed(efficient_frontier_upper_hull_cvar)(group) for group in cvar_groups
        )
        
        # Add traces for all CVaR groups
        for hull, name, color, width in zip(cvar_hulls, group_names, group_colors, group_widths):
            if hull:
                rx, ry = zip(*hull)
                cvar_frontier_fig.add_trace(
                    go.Scatter(
                        x=rx, y=ry, mode='lines',
                        line=dict(color=color, width=width),
                        name=f'Frontier ({name})'
                    )
                )
        
        # Add dots for individual candidates
        for port in all_visualization_candidates:
            # Use the same styling logic as in the original chart
            is_top_performer = port['optimization'].startswith('Top ')
            is_top_return = port['optimization'] == 'Max Return'
            
            # Set marker symbol based on candidate type
            if is_top_return:
                marker_symbol = 'x'
                marker_size = 10
            elif is_top_performer:
                marker_symbol = 'asterisk'
            else:
                marker_symbol = 'circle'
        
            # Determine marker color using the same logic as the original chart
            if port['optimization'] == 'Max Return':
                marker_color = 'blue'
            elif port['optimization'] == 'High Sharpe & Low MinVar':
                marker_color = 'orange'
            elif port['optimization'] == 'Min Variance':
                marker_color = 'cyan'
            elif port['optimization'] == 'MaxSS':
                marker_color = 'red'
            elif port['optimization'] == 'Max CF Sharpe':
                marker_color = 'violet'
            else:
                marker_color = (
                    'magenta' if port['optimization'] == 'Max Sharpe' or port['optimization'] == 'Top Sharpe'
                    else 'yellow' if port['optimization'] == 'Max Sortino' or port['optimization'] == 'Top Sortino'
                    else 'green' if port['optimization'] == 'Max Omega' or port['optimization'] == 'Top Omega'
                    else 'dodgerblue' if port['optimization'] == 'Max Calmar' or port['optimization'] == 'Top Calmar'
                    else 'gray'
                )
                
            # Override if candidate is clicked
            if selected_candidate and port['combo'] == selected_candidate.get("combo"):
                marker_color = "white"
                marker_size = 14
            else:
                marker_size = 10
            # Get the CVaR value or use 0 if not available
            cvar_value = port.get('cvar_95', 0)

            
            total = sum(abs(w) for w in port['weights'])
            normalized_weights = ", ".join(f"{w/total:.2f}" for w in port['weights'])
            tooltip = json.dumps({
                "combo": port['combo'],
                "weights": port['weights'],
                "optimization": port['optimization'],
                "risk": port.get('risk', 0), # Add risk
                "return": port.get('return', 0), # Add return
                "cvar_95": port.get('cvar_95', 0), # Add cvar_95 (already used for x-axis, but good to have in customdata too)
                "sharpe": port.get('sharpe', 0),
                "sortino": port.get('sortino', 0),
                "omega": port.get('omega', 0),
                "calmar": port.get('calmar', 0),
                "mod_sharpe": port.get('mod_sharpe', 0)
            })
            
            cvar_frontier_fig.add_trace(go.Scatter(
                x=[cvar_value], # Use CVaR for x-axis
                y=[port['return']],
                mode='markers',
                marker=dict(
                    size=12 if is_top_performer or is_top_return else 10,
                    color=marker_color,
                    symbol=marker_symbol,
                    line=dict(width=2, color='white') if is_top_performer or is_top_return else None
                ),
                customdata=[tooltip],
                meta=normalized_weights,
                hovertemplate=(
                    "CVaR 95%: %{x:.4f}<br>" + # Revert hover label
                    "Return: %{y:.4f}<br>" +
                    "Symbols: " + ", ".join(port['combo']) + "<br>" +
                    "Weights: %{meta}<br>" +
                    "Optimization: " + port['optimization'] + "<br>" +
                    "Sharpe: " + f"{port.get('sharpe', 0):.4f}" + "<br>" +
                    "Sortino: " + f"{port.get('sortino', 0):.4f}" + "<br>" +
                    "Omega: " + f"{port.get('omega', 0):.4f}" + "<br>" +
                    "Calmar: " + f"{port.get('calmar', 0):.4f}" + "<br>" +
                    "CF Sharpe: " + f"{port.get('mod_sharpe', 0):.4f}" + "<br>" +
                    "Ulcer Index: " + f"{port.get('ulcer_index', 0):.4f}" + "<br>" +
                    "Martin Ratio: " + f"{port.get('martin', 0):.4f}" + "<br>" +
                    "Pain Ratio: " + f"{port.get('pain_ratio', 0):.4f}" + "<br>" +
                    "Risk: " + f"{port.get('risk', 0):.4f}" + "<br>" +
                    "VaR 95%: " + f"{port.get('var_95', 0):.4f}<extra></extra>"
                )
            ))

        # Add reference lines at CVaR levels
        if max_cvar > 0:
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.90 * max_cvar,
                x1=0.90 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="red", dash="dot", width=2),
                name="90%"
            )
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.80 * max_cvar,
                x1=0.80 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="aqua", dash="dot", width=2),
                name="80%"
            )
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.60 * max_cvar,
                x1=0.60 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="lime", dash="dot", width=2),
                name="60%"
            )
            cvar_frontier_fig.add_shape(
                type="line",
                xref="x",
                yref="paper",
                x0=0.40 * max_cvar,
                x1=0.40 * max_cvar,
                y0=0,
                y1=1,
                line=dict(color="yellow", dash="dot", width=2),
                name="40%"
            )

        # Similarly for CVaR frontier:
        best_returns_cvar, best_metric_cvar, best_hedge_cvar, mid_frontier_cvar = find_recommended_portfolios(
            visualization_candidates, returns_df,
            cvar_hulls[4],  # Using the "Full" hull for CVaR
            type='cvar'
        )

        # Find recommended portfolios for CVaR frontier
        recommended_cvar_portfolios = [best_returns_cvar, best_metric_cvar, best_hedge_cvar, mid_frontier_cvar]

        # Update layout for the CVaR frontier chart
        cvar_frontier_fig.update_layout(
            title="Efficient Frontier (CVaR vs Return)", # Revert title
            xaxis_title="CVaR 95%", # Revert x-axis title
            xaxis=dict(
                range=[0, max_cvar + 0.0001], # Revert range calculation
                showgrid=True,
                gridcolor='rgba(100, 100, 100, 0.2)'
            ),
            yaxis_title="Return",
            height=1000,
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white'),
            updatemenus=[
                dict(
                    type="buttons",
                    direction="right",
                    buttons=[
                        dict(
                            args=[{"xaxis.autorange": True, "yaxis.autorange": True}],
                            label="Auto Range",
                            method="relayout"
                        ),
                        dict(
                            args=[{"xaxis.autorange": False, "xaxis.range": [0, max_cvar], # Revert range
                                "yaxis.autorange": True}],
                            label="Focus CVaR", # Revert button label
                            method="relayout"
                        )
                    ],
                    pad={"r": 10, "t": 10},
                    showactive=False,
                    x=0.15,
                    y=1.15,
                    xanchor="left",
                    yanchor="top",
                    bgcolor="rgba(60, 60, 60, 0.7)",
                    bordercolor="rgba(200, 200, 200, 0.5)"
                )
            ]
        )

        # Highlight the recommended portfolios on the charts with distinct markers
        if any(recommended_portfolios):
            for i, portfolio in enumerate(recommended_portfolios):
                if not portfolio:
                    continue
                    
                # Use different symbols for each recommendation - ADD FOURTH ELEMENT
                symbols = ['star', 'diamond', 'cross', 'triangle-up']
                colors = ['gold', 'cyan', 'lime', 'magenta']
                sizes = [16, 16, 16, 16]
                
                # Add special markers to standard frontier
                frontier_fig.add_trace(go.Scatter(
                    x=[portfolio['risk']],
                    y=[portfolio['return']],
                    mode='markers',
                    marker=dict(
                        size=sizes[i],
                        color=colors[i],
                        symbol=symbols[i],
                        line=dict(width=2, color='red')
                    ),
                    name=f"Recommendation {i+1}",
                    hoverinfo='skip'
                ))
                
                # Add correlation info to hover if available
                if 'correlation_to_best' in portfolio and i > 0:
                    frontier_fig.add_annotation(
                        x=portfolio['risk'],
                        y=portfolio['return'],
                        text=f"Corr: {portfolio['correlation_to_best']:.2f}",
                        font=dict(family="sans serif", size=18, color="red"),
                        showarrow=True,
                        arrowhead=2,
                        arrowcolor=colors[i],
                        ax=-40,
                        ay=-40
                    )

        # Do the same for CVaR frontier
        if any(recommended_cvar_portfolios):
            for i, portfolio in enumerate(recommended_cvar_portfolios):
                if not portfolio:
                    continue

                # Use different symbols for each recommendation - ADD FOURTH ELEMENT
                symbols = ['star', 'diamond', 'cross', 'triangle-up']
                colors = ['gold', 'cyan', 'lime', 'magenta']
                sizes = [16, 16, 16, 16]

                # Add special markers to CVaR frontier
                cvar_frontier_fig.add_trace(go.Scatter(
                    x=[portfolio.get('cvar_95', 0)],
                    y=[portfolio['return']],
                    mode='markers',
                    marker=dict(
                        size=sizes[i],
                        color=colors[i],
                        symbol=symbols[i],
                        line=dict(width=2, color='red')
                    ),
                    name=f"Recommendation {i+1}",
                    hoverinfo='skip'
                ))

                # Add correlation info to hover if available
                if 'correlation_to_best' in portfolio and i > 0:
                    cvar_frontier_fig.add_annotation(
                        x=portfolio.get('cvar_95', 0),
                        y=portfolio['return'],
                        text=f"Corr: {portfolio['correlation_to_best']:.2f}",
                        font=dict(family="sans serif", size=18, color="red"),
                        showarrow=True,
                        arrowhead=2,
                        arrowcolor=colors[i],
                        ax=-40,
                        ay=-40
                    )

        def normalize_portfolio(portfolio):
            if not portfolio:
                return None

            # Create a copy to avoid modifying the original
            normalized = portfolio.copy()

            # If weights exist, normalize them so absolute sum = 1.0
            if 'weights' in normalized and normalized['weights']:
                abs_sum = sum(abs(w) for w in normalized['weights'])
                if abs_sum > 0:
                    normalized['weights'] = [w/abs_sum for w in normalized['weights']]

            return normalized

        # --- Final Return ---
        # (This block is removed as normalize_portfolio is now defined above)

        # Apply normalization to both sets of portfolios
        recommended_portfolios = [normalize_portfolio(p) for p in recommended_portfolios]
        recommended_cvar_portfolios = [normalize_portfolio(p) for p in recommended_cvar_portfolios]

        # Store both combinations in JSON format
        risk_portfolios_json = json.dumps([p if p is None else {
            "combo": p.get("combo", []),
            "weights": p.get("weights", []),
            "optimization": p.get("optimization", ""),
            "return": float(p.get("return", 0)),
            "risk": float(p.get("risk", 0)),
            "sharpe": float(p.get("sharpe", 0)),
            "sortino": float(p.get("sortino", 0)),
            "omega": float(p.get("omega", 0)),
            "calmar": float(p.get("calmar", 0)),
            "cvar_95": float(p.get("cvar_95", 0))
        } for p in recommended_portfolios])

        cvar_portfolios_json = json.dumps([p if p is None else {
            "combo": p.get("combo", []),
            "weights": p.get("weights", []),
            "optimization": p.get("optimization", ""),
            "return": float(p.get("return", 0)),
            "risk": float(p.get("risk", 0)),
            "sharpe": float(p.get("sharpe", 0)), 
            "sortino": float(p.get("sortino", 0)),
            "omega": float(p.get("omega", 0)),
            "calmar": float(p.get("calmar", 0)),
            "cvar_95": float(p.get("cvar_95", 0))
        } for p in recommended_cvar_portfolios])

        # --- Market Phase Analysis is now handled by a separate callback ---

        # --- Final Return ---
        # Return tuple length must match the number of outputs (9)
        result_tuple = (table_content, frontier_fig, portfolio_returns_fig, False, combined_mpt_string, cvar_frontier_fig, # MPT/CVaR outputs
                       risk_portfolios_json, cvar_portfolios_json, clicked_portfolio_data_to_store) # Store outputs

        # Cache the result for weekend use (only during trading days)
        cache_friday_data(cache_key, result_tuple)

        return result_tuple

    # This except block should be indented to match the main 'try' block starting around line 151 (INDENTED)
    except Exception as e:
        print(f"Error in portfolio optimization: {e}")
        # Return default values including clearing the store on error
        empty_fig = go.Figure()
        empty_fig.update_layout(template="plotly_dark")
        # Ensure the return tuple matches the number of outputs (9)
        return ("Error during optimization", empty_fig, empty_fig, False, "", empty_fig, None, None, None) # MPT/CVaR outputs
# End of the update_portfolio_optimization function

if __name__ == "__main__":
    atexit.register(mt5.shutdown)
    
    app.run(debug=True, port=8053, dev_tools_hot_reload=False)
    #serve(app.server, host='0.0.0.0', port=8053, threads=4, _quiet=True)